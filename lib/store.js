import { create } from 'zustand'
import { persist } from 'zustand/middleware'

const useStore = create(
  persist(
    (set, get) => ({
      // Theme state
      theme: 'light',
      setTheme: (theme) => set({ theme }),
      
      // Diagram state
      nodes: [],
      edges: [],
      nodeIdCounter: 1,
      
      // Initialize with root node
      initializeRootNode: () => {
        const rootNode = {
          id: 'root',
          type: 'custom',
          position: { x: 400, y: 200 },
          data: { 
            label: 'Root Node',
            isEditing: false,
            children: []
          }
        }
        set({ 
          nodes: [rootNode],
          edges: [],
          nodeIdCounter: 2
        })
      },
      
      // Add new node
      addNode: (parentId, label) => {
        const { nodes, edges, nodeIdCounter } = get()
        const newNodeId = `node-${nodeIdCounter}`

        // Find parent node
        const parentNode = nodes.find(n => n.id === parentId)
        if (!parentNode) return

        // Calculate position for new node
        const childrenCount = parentNode.data.children.length
        const spacing = 250 // Horizontal spacing between siblings
        const verticalOffset = 150 // Vertical distance from parent

        // Center children around parent
        const startX = parentNode.position.x - (childrenCount * spacing) / 2
        const newPosition = {
          x: startX + childrenCount * spacing,
          y: parentNode.position.y + verticalOffset
        }

        // Create new node
        const newNode = {
          id: newNodeId,
          type: 'custom',
          position: newPosition,
          data: {
            label,
            isEditing: false,
            children: []
          }
        }

        // Create new edge
        const newEdge = {
          id: `edge-${parentId}-${newNodeId}`,
          source: parentId,
          target: newNodeId,
          type: 'smoothstep',
          style: {
            stroke: 'hsl(var(--primary))',
            strokeWidth: 2,
          }
        }

        // Update parent's children array
        const updatedNodes = nodes.map(node =>
          node.id === parentId
            ? { ...node, data: { ...node.data, children: [...node.data.children, newNodeId] }}
            : node
        )

        set({
          nodes: [...updatedNodes, newNode],
          edges: [...edges, newEdge],
          nodeIdCounter: nodeIdCounter + 1
        })
      },
      
      // Update node label
      updateNodeLabel: (nodeId, label) => {
        const { nodes } = get()
        const updatedNodes = nodes.map(node =>
          node.id === nodeId
            ? { ...node, data: { ...node.data, label } }
            : node
        )
        set({ nodes: updatedNodes })
      },
      
      // Toggle node editing
      toggleNodeEditing: (nodeId) => {
        const { nodes } = get()
        const updatedNodes = nodes.map(node =>
          node.id === nodeId
            ? { ...node, data: { ...node.data, isEditing: !node.data.isEditing } }
            : node
        )
        set({ nodes: updatedNodes })
      },
      
      // Clear diagram
      clearDiagram: () => {
        set({ nodes: [], edges: [], nodeIdCounter: 1 })
      },
      
      // Load user diagram
      loadUserDiagram: (userId) => {
        const savedDiagram = localStorage.getItem(`flowmind-diagram-${userId}`)
        if (savedDiagram) {
          const { nodes, edges, nodeIdCounter } = JSON.parse(savedDiagram)
          set({ nodes, edges, nodeIdCounter })
        } else {
          get().initializeRootNode()
        }
      },
      
      // Save user diagram
      saveUserDiagram: (userId) => {
        const { nodes, edges, nodeIdCounter } = get()
        localStorage.setItem(`flowmind-diagram-${userId}`, JSON.stringify({
          nodes,
          edges,
          nodeIdCounter
        }))
      }
    }),
    {
      name: 'flowmind-store',
      partialize: (state) => ({ theme: state.theme })
    }
  )
)

export default useStore
